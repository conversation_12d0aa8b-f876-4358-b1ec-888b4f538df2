﻿{
  "name": "maintenance-platform",
  "version": "1.0.0",
  "description": "Plateforme de maintenance industrielle",
  "private": true,
  "workspaces": [
    "frontend"
  ],
  "scripts": {
    "dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"",
    "dev:frontend": "cd frontend && npm run dev",
    "dev:backend": "cd backend && python -m uvicorn app.main:app --reload --host 0.0.0.0 --port 8000",
    "build": "npm --prefix frontend run build",
    "install:frontend": "npm --prefix frontend install",
    "install:all": "npm install && npm --prefix frontend install",
    "test": "npm --prefix frontend run test",
    "start": "concurrently \"npm run start:backend\" \"npm run start:frontend\"",
    "start:frontend": "npm --prefix frontend run preview",
    "start:backend": "cd backend && python -m uvicorn app.main:app --host 0.0.0.0 --port 8000"
  },
  "devDependencies": {
    "concurrently": "^7.6.0"
  }
}
