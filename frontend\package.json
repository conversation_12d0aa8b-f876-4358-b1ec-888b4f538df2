{"name": "maintenance-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "build:vercel": "npm run build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@fullcalendar/core": "^6.1.17", "@fullcalendar/daygrid": "^6.1.17", "@fullcalendar/interaction": "^6.1.17", "@fullcalendar/list": "^6.1.17", "@fullcalendar/react": "^6.1.17", "@fullcalendar/timegrid": "^6.1.17", "@types/node": "^24.0.7", "antd": "^5.26.2", "axios": "^1.3.4", "dayjs": "^1.11.13", "lucide-react": "^0.216.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.43.5", "react-query": "^3.39.3", "react-router-dom": "^6.8.1", "react-toastify": "^11.0.5"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.0.0", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "typescript": "^5.0.2", "vite": "^4.3.2", "vitest": "^0.29.8"}}