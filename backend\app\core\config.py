# Configuration de l'application
from pydantic_settings import BaseSettings
from typing import Optional

class Settings(BaseSettings):
    # Base de données
    DATABASE_URL: str = "postgresql://neondb_owner:<EMAIL>/neondb?sslmode=require"

    # Sécurité JWT
    SECRET_KEY: str = "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.***************************************************************************************************************************************************************************************************************************************************************************************************************.vxuF-cOriJjHq68QCnh9hzfsnmX55WUq86NZleCXJPI"
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30

    # Variables optionnelles
    REFRESH_TOKEN_EXPIRE_DAYS: Optional[int] = 7
    MAIL_USERNAME: Optional[str] = None
    MAIL_PASSWORD: Optional[str] = None
    MAIL_FROM: Optional[str] = None
    MAIL_PORT: Optional[int] = 587
    MAIL_SERVER: Optional[str] = None
    REDIS_URL: Optional[str] = None
    ENVIRONMENT: Optional[str] = "development"
    DEBUG: Optional[bool] = True

    # CORS
    ALLOWED_ORIGINS: list = [
        "http://localhost:5173",
        "http://localhost:3000",
        "https://maintenanceplatform-frontend.vercel.app",
        "https://maintenanceplatform-frontend-luor.vercel.app"
    ]

    class Config:
        env_file = ".env"
        extra = "ignore"  # Ignorer les variables supplémentaires

settings = Settings()
